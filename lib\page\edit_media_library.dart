import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:dandanplay_flutter/database/database.dart';
import 'package:dandanplay_flutter/service/media_library.dart';

class EditMediaLibraryPage extends StatefulWidget {
  final String? id;

  const EditMediaLibraryPage({super.key, this.id});

  @override
  State<EditMediaLibraryPage> createState() => _EditMediaLibraryPageState();
}

class _EditMediaLibraryPageState extends State<EditMediaLibraryPage> {
  final _formKey = GlobalKey<FormState>();
  MediaLibrary _mediaLibrary = MediaLibrary(
    id: '',
    name: '',
    url: '',
    mediaType: MediaType.webdav,
    addTime: DateTime.now().millisecondsSinceEpoch,
    isAnonymous: false,
  );
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();
  final _accountController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMediaLibrary();
  }

  Future<void> _loadMediaLibrary() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    MediaLibrary library;

    if (widget.id != null) {
      final result = await mediaLibraryService.getMediaLibrary(widget.id!);
      library =
          result ??
          MediaLibrary(
            id: '',
            name: '',
            url: '',
            mediaType: MediaType.webdav,
            addTime: 0,
            isAnonymous: false,
          );
    } else {
      library = MediaLibrary(
        id: '',
        name: '',
        url: '',
        mediaType: MediaType.webdav,
        addTime: 0,
        isAnonymous: false,
      );
    }

    setState(() {
      _mediaLibrary = library;
      _nameController.text = library.name;
      _urlController.text = library.url;
      _accountController.text = library.account ?? '';
      _passwordController.text = library.password ?? '';
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _saveMediaLibrary(BuildContext context) async {
    final nameError = _validateRequired(_nameController.text, '名称');
    final urlError = _validateUrl(_urlController.text);

    if (nameError != null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    if (urlError != null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final updatedLibrary = _mediaLibrary.copyWith(
      name: _nameController.text.trim(),
      url: _urlController.text.trim(),
      account:
          _accountController.text.trim().isEmpty
              ? null
              : _accountController.text.trim(),
      password:
          _passwordController.text.isEmpty ? null : _passwordController.text,
    );

    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    await mediaLibraryService.updateMediaLibrary(updatedLibrary);

    _nameController.clear();
    _urlController.clear();
    _accountController.clear();
    _passwordController.clear();
    setState(() {
      _isLoading = false;
    });
    if (context.mounted) {
      Navigator.of(context).pop();
    }
    return;
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName不能为空';
    }
    return null;
  }

  String? _validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'URL不能为空';
    }

    final uri = Uri.tryParse(value.trim());
    if (uri == null || !uri.hasScheme) {
      return '请输入有效的URL';
    }

    return null;
  }

  void showtoast(String message) {
    showFToast(
      context: context,
      title: const Text('Event has been created'),
      description: const Text('Friday, May 23, 2025 at 9:00 AM'),
      suffixBuilder:
          (context, entry, _) => IntrinsicHeight(
            child: FButton(
              style: context.theme.buttonStyles.primary.copyWith(
                contentStyle: context.theme.buttonStyles.primary.contentStyle
                    .copyWith(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 7.5,
                      ),
                      textStyle: FWidgetStateMap.all(
                        context.theme.typography.xs.copyWith(
                          color: context.theme.colors.primaryForeground,
                        ),
                      ),
                    ),
              ),
              onPress: entry.dismiss,
              child: const Text('Undo'),
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: FHeader(title: const Text('编辑媒体库')),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            FTextField(label: const Text('名称'), controller: _nameController),
            const SizedBox(height: 10),
            FTextField(label: const Text('URL'), controller: _urlController),
            const SizedBox(height: 10),
            FTextField(label: const Text('账号'), controller: _accountController),
            const SizedBox(height: 10),
            FTextField(
              label: const Text('密码'),
              obscureText: true,
              controller: _passwordController,
            ),
            const SizedBox(height: 10),
            FSwitch(
              label: const Text('匿名访问'),
              value: _mediaLibrary.isAnonymous,
              onChange: (value) {
                setState(() {
                  _mediaLibrary.isAnonymous = value;
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: FButton(
                    style: context.theme.buttonStyles.secondary,
                    onPress: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: FButton(
                    onPress:
                        _isLoading ? null : () => _saveMediaLibrary(context),
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('保存'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
