import 'package:dandanplay_flutter/database/database.dart';
import 'package:get_it/get_it.dart';
import 'package:drift/drift.dart';

class StorageService {
  late AppDatabase _database;

  static Future<void> register() async {
    final service = StorageService();
    await service.init();
    GetIt.I.registerSingleton<StorageService>(service);
  }

  Future<void> init() async {
    _database = AppDatabase();
  }

  // MediaLibrary 操作
  Future<List<MediaLibrary>> getMediaLibraries() async {
    return await _database.getAllMediaLibraries();
  }

  Future<MediaLibrary?> getMediaLibrary(String id) async {
    return await _database.getMediaLibrary(id);
  }

  Future<void> updateMediaLibrary(MediaLibrary mediaLibrary) async {
    await _database.insertOrUpdateMediaLibrary(mediaLibrary.toCompanion(false));
  }

  Future<void> deleteMediaLibrary(String id) async {
    await _database.deleteMediaLibrary(id);
  }

  // History 操作
  Future<List<History>> getHistories() async {
    return await _database.getAllHistories();
  }

  Future<History?> getHistory(String id) async {
    return await _database.getHistory(id);
  }

  Future<void> updateHistory(History history) async {
    await _database.insertOrUpdateHistory(history.toCompanion(false));
  }

  Future<void> deleteHistory(String id) async {
    await _database.deleteHistory(id);
  }

  // 关闭数据库连接
  Future<void> close() async {
    await _database.close();
  }
}
