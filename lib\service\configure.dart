import 'package:dandanplay_flutter/service/storage.dart';
import 'package:get_it/get_it.dart';

class ConfigureService {
  StorageService storage;
  ConfigureService({required this.storage});
  static Future<void> register() async {
    var service = ConfigureService(storage: GetIt.I.get<StorageService>());
    await service.init();
    GetIt.I.registerSingleton<ConfigureService>(service);
  }

  Future<void> init() async{

  }
}