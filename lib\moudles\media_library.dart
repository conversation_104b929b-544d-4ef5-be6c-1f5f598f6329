import 'package:hive_ce_flutter/hive_flutter.dart';

enum MediaType { webdav }

class MediaLibrary extends HiveObject {
  // uuid
  String id;
  String name;
  String url;
  MediaType mediaType;
  String? account;
  String? password;
  bool isAnonymous;
  int addTime;

  MediaLibrary({
    required this.id,
    required this.name,
    required this.url,
    required this.mediaType,
    required this.addTime,
    this.account,
    this.password,
    this.isAnonymous = false,
  });
}
