// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_adapters.dart';

// **************************************************************************
// AdaptersGenerator
// **************************************************************************

class MediaLibraryAdapter extends TypeAdapter<MediaLibrary> {
  @override
  final typeId = 0;

  @override
  MediaLibrary read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MediaLibrary(
      id: fields[0] as String,
      name: fields[1] as String,
      url: fields[2] as String,
      mediaType: fields[3] as MediaType,
      addTime: (fields[7] as num).toInt(),
      account: fields[4] as String?,
      password: fields[5] as String?,
      isAnonymous: fields[6] == null ? false : fields[6] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, MediaLibrary obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.url)
      ..writeByte(3)
      ..write(obj.mediaType)
      ..writeByte(4)
      ..write(obj.account)
      ..writeByte(5)
      ..write(obj.password)
      ..writeByte(6)
      ..write(obj.isAnonymous)
      ..writeByte(7)
      ..write(obj.addTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MediaLibraryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MediaTypeAdapter extends TypeAdapter<MediaType> {
  @override
  final typeId = 1;

  @override
  MediaType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MediaType.webdav;
      default:
        return MediaType.webdav;
    }
  }

  @override
  void write(BinaryWriter writer, MediaType obj) {
    switch (obj) {
      case MediaType.webdav:
        writer.writeByte(0);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MediaTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HistoryAdapter extends TypeAdapter<History> {
  @override
  final typeId = 2;

  @override
  History read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return History(
      id: fields[0] as String,
      duration: (fields[1] as num).toInt(),
      position: (fields[2] as num).toInt(),
      url: fields[3] as String,
      updateTime: (fields[4] as num).toInt(),
      snapshot: fields[5] as String,
      danmakuPath: fields[6] as String,
      danmakuUpdateTime: (fields[7] as num).toInt(),
    );
  }

  @override
  void write(BinaryWriter writer, History obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.duration)
      ..writeByte(2)
      ..write(obj.position)
      ..writeByte(3)
      ..write(obj.url)
      ..writeByte(4)
      ..write(obj.updateTime)
      ..writeByte(5)
      ..write(obj.snapshot)
      ..writeByte(6)
      ..write(obj.danmakuPath)
      ..writeByte(7)
      ..write(obj.danmakuUpdateTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HistoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
