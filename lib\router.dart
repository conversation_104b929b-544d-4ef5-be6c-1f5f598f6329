import 'package:dandanplay_flutter/page/edit_media_library.dart';
import 'package:dandanplay_flutter/page/root/root.dart';
import 'package:go_router/go_router.dart';

const String rootPath = '/';
const String editMediaLibraryPath = '/edit-media-library';

// GoRouter configuration
final router = GoRouter(
  initialLocation: rootPath,
  routes: [
    GoRoute(path: rootPath, builder: (context, state) => const RootPage()),
    GoRoute(
      path: editMediaLibraryPath,
      builder: (context, state) => const EditMediaLibraryPage(),
    ),
  ],
);
