import 'package:dandanplay_flutter/moudles/media_library.dart';
import 'package:dandanplay_flutter/hive/hive_registrar.g.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';

class StorageService {
  static late Box<MediaLibrary> mediaLibraryBox;

  static Future<void> register() async {
    final service = StorageService();
    await service.init();
    GetIt.I.registerSingleton<StorageService>(service);
  }

  Future<void> init() async {
    await Hive.initFlutter();
    Hive.registerAdapters();
    mediaLibraryBox = await Hive.openBox<MediaLibrary>('mediaLibrary');
  }

  Future<List<MediaLibrary>> getMediaLibraries() async {
    return mediaLibraryBox.values.toList();
  }

  Future<MediaLibrary?> getMediaLibrary(String id) async {
    return mediaLibraryBox.get(id);
  }

  Future<void> updateMediaLibrary(MediaLibrary mediaLibrary) async {
    await mediaLibraryBox.put(mediaLibrary.id, mediaLibrary);
  }

  Future<void> deleteMediaLibrary(String id) async {
    await mediaLibraryBox.delete(id);
  }
}
