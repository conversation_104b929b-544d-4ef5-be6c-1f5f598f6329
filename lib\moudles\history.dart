import 'package:hive_ce_flutter/hive_flutter.dart';

class History extends HiveObject {
  String id;
  int duration;
  int position;
  String url;
  int updateTime;
  String snapshot;
  String danmakuPath;
  int danmakuUpdateTime;

  History({
    required this.id,
    required this.duration,
    required this.position,
    required this.url,
    required this.updateTime,
    required this.snapshot,
    required this.danmakuPath,
    required this.danmakuUpdateTime,
  });
}
