import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

part 'database.g.dart';

// 媒体类型枚举
enum MediaType { webdav }

// 媒体库表定义
class MediaLibraries extends Table {
  TextColumn get id => text()();
  TextColumn get name => text()();
  TextColumn get url => text()();
  IntColumn get mediaType => intEnum<MediaType>()();
  TextColumn get account => text().nullable()();
  TextColumn get password => text().nullable()();
  BoolColumn get isAnonymous => boolean().withDefault(const Constant(false))();
  IntColumn get addTime => integer()();

  @override
  Set<Column> get primaryKey => {id};
}

// 历史记录表定义
class Histories extends Table {
  TextColumn get id => text()();
  IntColumn get duration => integer()();
  IntColumn get position => integer()();
  TextColumn get url => text()();
  IntColumn get updateTime => integer()();
  TextColumn get snapshot => text()();
  TextColumn get danmakuPath => text()();
  IntColumn get danmakuUpdateTime => integer()();

  @override
  Set<Column> get primaryKey => {id};
}

// 数据库类
@DriftDatabase(tables: [MediaLibraries, Histories])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  // MediaLibrary 操作
  Future<List<MediaLibrary>> getAllMediaLibraries() =>
      select(mediaLibraries).get();

  Future<MediaLibrary?> getMediaLibrary(String id) =>
      (select(mediaLibraries)
        ..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

  Future<void> insertOrUpdateMediaLibrary(
    MediaLibrariesCompanion mediaLibrary,
  ) => into(mediaLibraries).insertOnConflictUpdate(mediaLibrary);

  Future<void> deleteMediaLibrary(String id) =>
      (delete(mediaLibraries)..where((tbl) => tbl.id.equals(id))).go();

  // History 操作
  Future<List<History>> getAllHistories() => select(histories).get();

  Future<History?> getHistory(String id) =>
      (select(histories)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

  Future<void> insertOrUpdateHistory(HistoriesCompanion history) =>
      into(histories).insertOnConflictUpdate(history);

  Future<void> deleteHistory(String id) =>
      (delete(histories)..where((tbl) => tbl.id.equals(id))).go();
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'db.sqlite'));
    return NativeDatabase.createInBackground(file);
  });
}
