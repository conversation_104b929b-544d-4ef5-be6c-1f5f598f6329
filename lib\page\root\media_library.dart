import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:dandanplay_flutter/service/media_library.dart';

class MediaLibraryPage extends StatelessWidget {
  const MediaLibraryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();

    return Watch((_) {
      final libraries = mediaLibraryService.mediaLibraries.value;

      return FTileGroup(
        divider: FTileDivider.indented,
        style: tileGroupStyle(
          colors: context.theme.colors,
          typography: context.theme.typography,
          style: context.theme.style,
          newColors: context.theme.colors.copyWith(
            border: Color.fromARGB(0, 238, 238, 238),
          ),
        ),
        children:
            libraries
                .map(
                  (library) => FTile(
                    prefixIcon: const Icon(FIcons.folder),
                    title: Text(library.name),
                    subtitle: Text(library.url),
                  ),
                )
                .toList(),
      );
    });
  }
}
