import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // register services
  await StorageService.register();
  await MediaLibraryService.register();
  await ConfigureService.register();

  runApp(const Application());
}

class Application extends StatelessWidget {
  const Application({super.key});
  @override
  Widget build(BuildContext context) => MaterialApp.router(
    builder:
        (context, child) =>
            FToaster(child: FTheme(data: FThemes.blue.light, child: child!)),
    routerConfig: router,
  );
}
