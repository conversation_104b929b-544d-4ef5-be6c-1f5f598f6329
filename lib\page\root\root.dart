import 'package:dandanplay_flutter/page/root/home.dart';
import 'package:dandanplay_flutter/page/root/media_library.dart';
import 'package:dandanplay_flutter/page/root/my.dart';
import 'package:dandanplay_flutter/router.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';

final pages = [const HomePage(), const MediaLibraryPage(), const MyPage()];

class RootPage extends StatefulWidget {
  const RootPage({super.key});

  @override
  State<RootPage> createState() => RootPageState();
}

class RootPageState extends State<RootPage> with TickerProviderStateMixin {
  int index = 1;

  var headers = [];

  @override
  void initState() {
    super.initState();
    var controller = FPopoverController(vsync: this);
    setState(() {
      headers = [
        const FHeader(title: Text('首页')),
        FHeader(
          title: const Text('媒体库'),
          suffixes: [
            FPopoverMenu.automatic(
              popoverController: controller,
              menuAnchor: Alignment.topRight,
              childAnchor: Alignment.bottomRight,
              menu: [
                FTileGroup(
                  children: [
                    FTile(
                      prefixIcon: Icon(FIcons.library),
                      title: const Text('添加媒体库'),
                      onPress: () {
                        controller.toggle();
                        context.push(editMediaLibraryPath);
                      },
                    ),
                  ],
                ),
              ],
              child: FHeaderAction(
                icon: Icon(FIcons.ellipsis),
                onPress: controller.toggle,
              ),
            ),
          ],
        ),
        const FHeader(title: Text('个人中心')),
      ];
    });
  }

  @override
  Widget build(BuildContext context) => FScaffold(
    scaffoldStyle: context.theme.scaffoldStyle.copyWith(
      childPadding: EdgeInsets.all(0),
    ),
    header: Padding(padding: EdgeInsets.all(0), child: headers[index]),
    footer: FBottomNavigationBar(
      index: index,
      onChange: (index) => setState(() => this.index = index),
      children: const [
        FBottomNavigationBarItem(icon: Icon(FIcons.house), label: Text('首页')),
        FBottomNavigationBarItem(
          icon: Icon(FIcons.library),
          label: Text('媒体库'),
        ),
        FBottomNavigationBarItem(icon: Icon(FIcons.user), label: Text('个人中心')),
      ],
    ),
    child: pages[index],
  );
}
