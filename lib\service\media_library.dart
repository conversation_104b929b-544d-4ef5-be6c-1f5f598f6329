import 'package:dandanplay_flutter/database/database.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:uuid/uuid.dart';

final uuid = Uuid();

class MediaLibraryService {
  StorageService storage;

  MediaLibraryService({required this.storage});

  final Signal<List<MediaLibrary>> mediaLibraries = signal([]);

  static Future<void> register() async {
    var service = MediaLibraryService(storage: GetIt.I.get<StorageService>());
    await service.init();
    GetIt.I.registerSingleton<MediaLibraryService>(service);
  }

  Future<void> init() async {
    final libraries = await storage.getMediaLibraries();
    libraries.sort((a, b) => b.addTime.compareTo(a.addTime));
    mediaLibraries.value = libraries;
  }

  Future<MediaLibrary?> getMediaLibrary(String id) async {
    return await storage.getMediaLibrary(id);
  }

  Future<void> updateMediaLibrary(MediaLibrary mediaLibrary) async {
    MediaLibrary updatedLibrary = mediaLibrary;
    if (mediaLibrary.id.isEmpty) {
      updatedLibrary = mediaLibrary.copyWith(
        id: uuid.v4(),
        addTime: DateTime.now().millisecondsSinceEpoch,
      );
    }
    await storage.updateMediaLibrary(updatedLibrary);

    // Update the reactive state
    final updatedLibraries = await storage.getMediaLibraries();
    mediaLibraries.value = updatedLibraries;
  }

  Future<void> deleteMediaLibrary(String id) async {
    await storage.deleteMediaLibrary(id);

    // Update the reactive state
    final updatedLibraries = await storage.getMediaLibraries();
    mediaLibraries.value = updatedLibraries;
  }
}
